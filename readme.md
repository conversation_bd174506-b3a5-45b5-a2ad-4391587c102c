## 一、部署方式

### 方式一：nginx部署
这种方式请求不经过api网关，直接从nginx转发到微服务，通过nginx配置负载均衡。
多节点时必须开启nginx的hash映射，避免文件的分片落在不同节点无法合并。
```

upstream ups-file-upload {
  # 根据ip固定hash到同一节点，避免文件的分片落在不同节点无法合并
  ip_hash;
  server ip1:19020;
  server ip2:19020;
}

server {
  listen       8999(8998);
  # fpaas文件上传，注意要放在/webapi/这个location前面
  location /webapi/FPAAS-FILE-UPLOAD/ {
      # 报文大小限制
      client_max_body_size 10m;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      chunked_transfer_encoding off;
      proxy_pass http://ups-file-upload/;
  }
  # api网关
  location /webapi/ {
      ...
  }
  
}
```

### 方式二：fpaas网关
这种方式请求会经过api网关转发到上传服务。需要配置网关提供者和API。

#### 1、api提供者：
即微服务所在注册中心，如nacos://********:8848，注意提供者类型选择springCloud
#### 2、api接口:

| apiKey                                  | api类型       | 请求路径                                                   | 调用方式 | api提供者 |
|-----------------------------------------|-------------|--------------------------------------------------------|------|--------|
| FPAAS-FILE-UPLOAD/upload/split/check    | springCloud | FPAAS-FILE-UPLOAD/upload/split/check                   | POST | 上面的提供者 |
| FPAAS-FILE-UPLOAD/upload/split/process  | springCloud | FPAAS-FILE-UPLOAD/upload/split/process                 | POST | 上面的提供者 |
| FPAAS-FILE-UPLOAD/heartbeat             | springCloud | FPAAS-FILE-UPLOAD/heartbeat                            | POST | 上面的提供者 |

## 二、接口
### step1.分片上传状态检查
```
curl --location 'http://localhost:8084/at/upload/split/check' \
--header 'Content-Type: application/json' \
--header 'App-id: 12345678' \
--header 'Workspace-id: default' \
--data '{
    "fileKey":"文件md5值",
    "fileSize":"文件大小，数字，单位为byte",
    "fileOrigName":"原始文件名称"
}'
```
```
响应：json
{
	"_Return":"响应码，000000为成功，其他失败",
	"_RejMessage":"响应消息",
	"data":{
		"fileKey":"文件ID，取文件md5摘要",
		"splitSize":"数字类型，分片大小，单位byte",
		"splitCount":"数字类型，分片数，按照分片大小从头开始切分，最后1个分片大小一般不足分片大小，按实际大小处理",
		"todoIndexes": [数字类型，待上传的分片索引0,待上传的分片索引1，注意索引从0开始],
		"filePath":"服务器文件目录，全部上传成功后可用",
		"fileName":"服务器文件名称，全部上传成功后可用"
	}
}
```
### step2.分片文件上传，请求为form表单，响应为json
```
curl --location 'http://localhost:8084/at/upload/split/process' \
--header 'Content-Type: multipart/form-data' \
--header 'App-id: 12345678' \
--header 'Workspace-id: default' \
--form 'file=@"分片文件"' \
--form 'index="分片索引，注意索引从0开始"' \
--form 'size="此分片文件大小，单位byte"' \
--form 'fileKey="文件key，原文件md5"' \
--form 'splitKey="分片文件key，分片文件md5"'
```
```
响应：json格式
{
	"_Return":"响应码，000000为成功，其他失败",
	"_RejMessage":"响应消息"
}
```

### step2、上传分片（form表单）
将上一步的加密字节数组放入formdata表单的file域中。
```java
    List<byte[]> chunks;
    Int[] todoIndexes = [];
    for (int i = 0; i < chunks.size(); i++) {

        if (!todoIndexes.contains(i)) {
            continue;
        }
        byte[] chunk = chunks.get(i);

        HttpPost postProcess = new HttpPost("http://localhost:19020/upload/split/process");
        postProcess.setHeader("app-id", "18026780");
        postProcess.setHeader("workspace-id", "default");
        MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();
        entityBuilder.addTextBody("fileKey", fileKey);
        entityBuilder.addTextBody("index", i + "");
        entityBuilder.addTextBody("size", chunk.length + "");
        entityBuilder.addTextBody("splitKey", DigestUtils.md5Hex(chunk));
        entityBuilder.addBinaryBody("file", chunk, ContentType.DEFAULT_BINARY, origFileName + "." + i);
        postProcess.setEntity(entityBuilder.build());

        try (CloseableHttpResponse processResp = httpClient.execute(postProcess)) {
            String jsonString = IoUtil.read(processResp.getEntity().getContent(), StandardCharsets.UTF_8);
            restResult = JSONUtil.toBean(jsonString, RestResult.class);
            System.out.println("process响应：" + jsonString);
            if (!restResult.isOk()) {
                break;
            }
        }
    }
```

## 三、文件加密
加密是对上传文件本身进行加密，并非全报文加密。
密钥应用范围为fpaas应用+环境。即不同应用或不同环境的密钥都可单独配置。
目前支持SM4加密方式，如需扩展，可查看fpaas.upload.decryptor.DecryptorFactory 类。
### 1、启用文件加密
```
fpaas:
  file-upload:
    # 文件加解密类型，NONE/SM2/RSA/SM4
    encrypt-type: SM4
```

### 2、服务端密钥配置
进入fpaas后管，选择应用和环境，打开菜单【基本配置】-【密钥管理】，添加密钥：
密钥类型：SM4
密钥标识：文件上传
密钥内容：32字符的SM4对称密钥

### 3、分片加密
根据check接口结果里的分片大小对文件进行切割分片。例如设置分片大小为1M，那么上传2.5M文件时分片大小依次为：1M、1M、0.5M。
加密采用每个分片单独加密，而不是整个文件加密再分片。需要注意分片加密后长度可能会变化。
再对分片字节数组进行sm4加密，生成加密后的字节数组。
未达到分片大小时，即只有1个分片时，按1个分片加密即可。

## 四、鉴权
<font color=red>注意，如果部署方式为经过fpaas网关，无须开启鉴权。</font>

默认鉴权方式为发送请求到fpaas网关，检查请求头中appId、workspaceId和token是否有效。
### 开启鉴权
```yaml
fpaas:
  file-upload:
    # 鉴权开关，部署不经过网关时需要开启鉴权
    auth-available: true
    # 鉴权服务，通过访问fpaas网关，能正常返回即可。此接口要在fpaas网关中配置
    auth-url: http://127.0.0.1:8999/webapi/FPAAS-FILE-UPLOAD/heartbeat
```
### 自定义鉴权
自定义扩展类，实现fpaas.upload.checker.IAuthChecker接口，自行实现鉴权逻辑，并注入bean，bean名称为authChecker。