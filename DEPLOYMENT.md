# FPAAS File Upload Service - AWS EKS Deployment Guide

This guide provides comprehensive instructions for containerizing and deploying the FPAAS File Upload Service to AWS EKS (Elastic Kubernetes Service).

## Overview

The FPAAS File Upload Service is a Java Spring Boot application that provides chunked file upload capabilities with encryption support. This deployment containerizes the existing binary-based service for Kubernetes deployment.

### Key Features
- **Chunked File Upload**: Supports large file uploads through chunking
- **File Encryption**: SM4 encryption support for uploaded files
- **Multiple Storage Backends**: MinIO object storage integration
- **Service Discovery**: Nacos and Consul support
- **Health Monitoring**: Actuator endpoints for health checks
- **Horizontal Scaling**: Kubernetes-native scaling capabilities

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AWS ALB       │    │   NGINX Ingress │    │   EKS Cluster   │
│   (Optional)    │───▶│   Controller    │───▶│                 │
└─────────────────┘    └─────────────────┘    │  ┌───────────┐  │
                                              │  │   Pod 1   │  │
┌─────────────────┐    ┌─────────────────┐    │  ├───────────┤  │
│   MinIO         │    │   MySQL RDS     │    │  │   Pod 2   │  │
│   (Object       │◀───┤   (Database)    │◀───┤  ├───────────┤  │
│   Storage)      │    │                 │    │  │   Pod N   │  │
└─────────────────┘    └─────────────────┘    │  └───────────┘  │
                                              └─────────────────┘
```

## Prerequisites

### Infrastructure Requirements
- **AWS EKS Cluster** (Kubernetes 1.19+)
- **MySQL Database** (RDS recommended)
- **MinIO Object Storage** (or S3-compatible storage)
- **Container Registry** (ECR, Docker Hub, etc.)
- **Helm 3.0+** for deployment
- **kubectl** configured for your EKS cluster

### Optional Components
- **Nacos** for service discovery
- **Consul** for service discovery
- **Prometheus** for monitoring
- **Grafana** for visualization

## Quick Start

### 1. Build and Push Docker Image

```bash
# Build the Docker image
docker build -t your-registry/fpaas-file-upload:2.6.0 .

# Push to your container registry
docker push your-registry/fpaas-file-upload:2.6.0
```

### 2. Configure Environment

Edit the appropriate values file for your environment:

```bash
# For development
cp helm/fpaas-file-upload/values-dev.yaml helm/fpaas-file-upload/values-dev-custom.yaml

# For production
cp helm/fpaas-file-upload/values-prod.yaml helm/fpaas-file-upload/values-prod-custom.yaml
```

Update the following key configurations:

```yaml
# Database configuration
database:
  host: your-mysql-host.rds.amazonaws.com
  password: your-secure-password

# MinIO configuration
minio:
  endpoint: http://your-minio-host:9000
  secretKey: your-minio-secret-key

# Image configuration
image:
  registry: your-registry
  tag: 2.6.0
```

### 3. Deploy Using Script

```bash
# Deploy to development
./scripts/build-and-deploy.sh -e dev -r your-registry

# Deploy to production
./scripts/build-and-deploy.sh -e prod -r your-registry -t 2.6.0
```

### 4. Verify Deployment

```bash
# Check pod status
kubectl get pods -n fpaas-prod

# Check service
kubectl get svc -n fpaas-prod

# Check ingress (if enabled)
kubectl get ingress -n fpaas-prod
```

## Detailed Configuration

### Docker Configuration

The Dockerfile includes several optimizations:

- **Multi-stage build** for smaller image size
- **Non-root user** for security
- **Health checks** built-in
- **Proper signal handling** for graceful shutdown

### Kubernetes Resources

The Helm chart creates the following resources:

- **Deployment**: Main application pods
- **Service**: Internal service discovery
- **ConfigMap**: Non-sensitive configuration
- **Secret**: Sensitive data (passwords, keys)
- **PVC**: Persistent storage for temporary files
- **Ingress**: External access (optional)
- **HPA**: Horizontal Pod Autoscaler (optional)
- **ServiceMonitor**: Prometheus monitoring (optional)

### Environment-Specific Configurations

#### Development Environment
- **Resources**: 512Mi memory, 250m CPU
- **Replicas**: 1
- **Storage**: 5Gi
- **Encryption**: Disabled for simplicity
- **Monitoring**: Basic health checks

#### Staging Environment
- **Resources**: 768Mi memory, 375m CPU
- **Replicas**: 2
- **Storage**: 20Gi
- **Encryption**: SM4 enabled
- **Monitoring**: Full monitoring with ServiceMonitor

#### Production Environment
- **Resources**: 1024Mi memory, 500m CPU
- **Replicas**: 3 (with anti-affinity)
- **Storage**: 100Gi
- **Encryption**: SM4 enabled
- **Monitoring**: Full monitoring with alerting

## Security Considerations

### Container Security
- Runs as non-root user (UID 1000)
- Read-only root filesystem where possible
- Minimal base image (OpenJDK slim)
- Regular security updates

### Kubernetes Security
- Service accounts with minimal permissions
- Network policies for traffic isolation
- Pod security contexts
- Secret management for sensitive data

### Application Security
- SM4 file encryption
- Configurable authentication
- Input validation and sanitization
- Secure database connections

## Monitoring and Observability

### Health Checks
- **Liveness Probe**: `/actuator/health/liveness`
- **Readiness Probe**: `/actuator/health/readiness`
- **Startup Probe**: Configured for slow-starting containers

### Metrics
- **Prometheus Metrics**: `/actuator/prometheus`
- **JVM Metrics**: Memory, GC, threads
- **Application Metrics**: File upload statistics
- **Custom Metrics**: Business-specific metrics

### Logging
- **Structured Logging**: JSON format for log aggregation
- **Log Levels**: Configurable per environment
- **Log Rotation**: Automatic log rotation
- **Centralized Logging**: Integration with ELK stack

## Scaling and Performance

### Horizontal Scaling
```yaml
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
```

### Vertical Scaling
```yaml
resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

### Performance Tuning
- **JVM Tuning**: Optimized heap and GC settings
- **Connection Pooling**: Database connection optimization
- **Caching**: Application-level caching where appropriate
- **Resource Limits**: Proper CPU and memory limits

## Troubleshooting

### Common Issues

#### Pod Startup Issues
```bash
# Check pod events
kubectl describe pod <pod-name> -n fpaas-prod

# Check logs
kubectl logs <pod-name> -n fpaas-prod

# Check resource usage
kubectl top pod <pod-name> -n fpaas-prod
```

#### Database Connection Issues
```bash
# Test database connectivity
kubectl exec -it <pod-name> -n fpaas-prod -- nc -zv <db-host> 3306

# Check database credentials
kubectl get secret fpaas-file-upload-secret -n fpaas-prod -o yaml
```

#### Storage Issues
```bash
# Check PVC status
kubectl get pvc -n fpaas-prod

# Check storage class
kubectl get storageclass

# Check volume mounts
kubectl describe pod <pod-name> -n fpaas-prod
```

### Performance Issues
```bash
# Check resource usage
kubectl top pods -n fpaas-prod

# Check HPA status
kubectl get hpa -n fpaas-prod

# Check node resources
kubectl top nodes
```

## Maintenance

### Updates and Upgrades
```bash
# Update to new version
helm upgrade fpaas-file-upload ./helm/fpaas-file-upload \
  -f ./helm/fpaas-file-upload/values-prod.yaml \
  --namespace fpaas-prod \
  --set image.tag=2.7.0
```

### Backup and Recovery
- **Database Backups**: Regular RDS snapshots
- **Configuration Backups**: Helm values and secrets
- **Application Data**: MinIO bucket backups

### Monitoring and Alerting
- **Resource Usage**: CPU, memory, disk alerts
- **Application Health**: Health check failures
- **Performance**: Response time and throughput
- **Error Rates**: Application and infrastructure errors

## Support and Troubleshooting

For additional support:
1. Check the application logs
2. Review Kubernetes events
3. Verify configuration values
4. Contact the Platform Team

## Next Steps

After successful deployment:
1. Set up monitoring dashboards
2. Configure alerting rules
3. Implement backup strategies
4. Plan for disaster recovery
5. Document operational procedures
