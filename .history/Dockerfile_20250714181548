# Multi-stage Dockerfile for FPAAS File Upload Service
# Stage 1: Base runtime image with security hardening
FROM openjdk:8-jre-alpine3.9 as base

# Create non-root user for security
RUN groupadd -r fpaas && useradd -r -g fpaas -s /bin/false fpaas

# Install required packages and clean up in single layer
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        curl \
        ca-certificates && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Stage 2: Application runtime
FROM base as runtime

# Set working directory
WORKDIR /app

# Create necessary directories with proper permissions
RUN mkdir -p /app/config /app/lib /app/logs /tmp/fpaas-temp && \
    chown -R fpaas:fpaas /app /tmp/fpaas-temp

# Copy application files
COPY --chown=fpaas:fpaas fpaas-file-upload-2.6.0.jar /app/
COPY --chown=fpaas:fpaas lib/ /app/lib/
COPY --chown=fpaas:fpaas config/ /app/config/

# Set environment variables
ENV JAVA_OPTS="-server -Xms512m -Xmx1024m -Xmn256m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -XX:-OmitStackTraceInFastThrow"
ENV SPRING_PROFILES_ACTIVE=k8s
ENV SERVER_PORT=19020
ENV MANAGEMENT_SERVER_PORT=19021

# Expose ports
EXPOSE 19020 19021

# Switch to non-root user
USER fpaas

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:19021/actuator/health || exit 1

# Start the application
CMD ["sh", "-c", "java ${JAVA_OPTS} -jar -Djava.ext.dirs=/app:/app/lib:${JAVA_HOME}/lib/ext -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE} /app/fpaas-file-upload-2.6.0.jar"]
