# Default values for fpaas-file-upload
# This is a YAML-formatted file.

# Global settings
global:
  imageRegistry: ""
  imagePullSecrets: []

# Image configuration
image:
  registry: ""
  repository: fpaas-file-upload
  tag: "2.6.0"
  pullPolicy: IfNotPresent

# Deployment configuration
replicaCount: 2

# Service configuration
service:
  type: ClusterIP
  port: 80
  targetPort: 19020
  annotations: {}

# Ingress configuration
ingress:
  enabled: false
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
  hosts:
    - host: fpaas-file-upload.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Resource limits and requests
resources:
  limits:
    cpu: 1000m
    memory: 1536Mi
  requests:
    cpu: 500m
    memory: 1024Mi

# Autoscaling configuration
autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Health checks
livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: management
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: management
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 1000

# Pod security context
podSecurityContext:
  fsGroup: 1000

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod annotations and labels
podAnnotations: {}
podLabels: {}

# Node selector, tolerations, and affinity
nodeSelector: {}
tolerations: []
affinity: {}

# Environment-specific configuration
environment: dev

# Application configuration
config:
  server:
    port: 19020
  management:
    server:
      port: 19021
    endpoints:
      web:
        exposure:
          include: "health,info,metrics,prometheus"
  spring:
    profiles:
      active: k8s
    application:
      name: FPAAS-FILE-UPLOAD

# External dependencies configuration
database:
  host: ""
  port: 3306
  name: fpaas
  username: fpaas
  # Password should be provided via secret

minio:
  endpoint: ""
  accessKey: minioadmin
  # secretKey should be provided via secret
  buckets: fpaas-bucket

nacos:
  serverAddr: ""
  namespace: ""

consul:
  host: ""
  port: 8500

# File upload specific configuration
fileUpload:
  splitSize: 1024
  uploadDir: file-upload
  tmpDir: /tmp/fpaas-temp
  tmpExpireDays: 7
  taskMaxThread: 8
  taskCoreThread: 2
  taskQueueSize: 100
  encryptType: SM4
  authAvailable: false
  maxFileSize: 100MB
  maxRequestSize: 100MB

# JVM configuration
jvm:
  xms: 512m
  xmx: 1024m
  xmn: 256m
  metaspaceSize: 256m
  maxMetaspaceSize: 512m

# Persistent volume configuration
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 10Gi
  mountPath: /tmp/fpaas-temp

# Monitoring and observability
monitoring:
  enabled: true
  serviceMonitor:
    enabled: false
    namespace: monitoring
    interval: 30s
    path: /actuator/prometheus
