# Staging environment values for fpaas-file-upload
# This file overrides the default values.yaml for staging environment

# Environment identifier
environment: staging

# Image configuration for staging
image:
  tag: "staging-latest"
  pullPolicy: IfNotPresent

# Staging resources (between dev and prod)
replicaCount: 2

resources:
  limits:
    cpu: 750m
    memory: 1280Mi
  requests:
    cpu: 375m
    memory: 768Mi

# JVM settings for staging
jvm:
  xms: 384m
  xmx: 768m
  xmn: 192m
  metaspaceSize: 192m
  maxMetaspaceSize: 384m

# Staging database configuration
database:
  host: staging-onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
  port: 3306
  name: fpaas_staging
  username: fpaas_staging
  password: "staging-password-change-me"

# Staging MinIO configuration
minio:
  endpoint: http://staging-minio-host:9000
  accessKey: minioadmin
  secretKey: staging-minio-secret-change-me
  buckets: fpaas-bucket-staging

# Staging Nacos configuration
nacos:
  serverAddr: staging-nacos-host
  namespace: staging-namespace

# Staging Consul configuration
consul:
  host: staging-consul-host
  port: 8500

# Staging ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
  hosts:
    - host: fpaas-file-upload-staging.yourcompany.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: fpaas-file-upload-staging-tls
      hosts:
        - fpaas-file-upload-staging.yourcompany.com

# Enable autoscaling in staging
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Staging monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: monitoring
    interval: 30s

# Staging persistence
persistence:
  enabled: true
  storageClass: "gp2"
  size: 20Gi

# Staging file upload configuration
fileUpload:
  splitSize: 1024
  tmpExpireDays: 5
  taskMaxThread: 6
  taskCoreThread: 2
  taskQueueSize: 75
  encryptType: SM4
  authAvailable: true
  sm4Key: "staging-sm4-key-32-chars-long"
  authUrl: "http://staging-gateway:8999/webapi/FPAAS-FILE-UPLOAD/heartbeat"

# Staging-specific pod annotations
podAnnotations:
  environment: "staging"
  
# Staging affinity for better distribution
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - fpaas-file-upload
        topologyKey: kubernetes.io/hostname
