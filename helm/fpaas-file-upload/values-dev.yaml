# Development environment values for fpaas-file-upload
# This file overrides the default values.yaml for development environment

# Environment identifier
environment: dev

# Image configuration for development
image:
  tag: "dev-latest"
  pullPolicy: Always

# Reduced resources for development
replicaCount: 1

resources:
  limits:
    cpu: 500m
    memory: 1Gi
  requests:
    cpu: 250m
    memory: 512Mi

# JVM settings for development
jvm:
  xms: 256m
  xmx: 512m
  xmn: 128m
  metaspaceSize: 128m
  maxMetaspaceSize: 256m

# Development database configuration
database:
  host: dev-onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
  port: 3306
  name: deploydb
  username: deploy
  password: "1q2w3e4R!@#$"

# Development MinIO configuration
minio:
  endpoint: http://dev-minio-host:9000
  accessKey: minioadmin
  secretKey: minioadmin
  buckets: fpaas-bucket

# Development Nacos configuration
nacos:
  serverAddr: dev-nacos-host
  namespace: ""

# Development Consul configuration
consul:
  host: dev-consul-host
  port: 8500

# Development ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
  hosts:
    - host: fpaas-file-upload-dev.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Disable autoscaling in development
autoscaling:
  enabled: false

# Development monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: false

# Development persistence
persistence:
  enabled: true
  storageClass: "gp2"
  size: 5Gi

# Development file upload configuration
fileUpload:
  splitSize: 512
  tmpExpireDays: 3
  taskMaxThread: 4
  taskCoreThread: 1
  taskQueueSize: 50
  encryptType: NONE
  authAvailable: false

# Development-specific pod annotations
podAnnotations:
  environment: "development"
  
# Development node selector (if needed)
nodeSelector:
  environment: development
