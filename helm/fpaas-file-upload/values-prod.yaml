# Production environment values for fpaas-file-upload
# This file overrides the default values.yaml for production environment

# Environment identifier
environment: prod

# Image configuration for production
image:
  tag: "2.6.0"
  pullPolicy: IfNotPresent

# Production resources (full allocation)
replicaCount: 3

resources:
  limits:
    cpu: 1000m
    memory: 1536Mi
  requests:
    cpu: 500m
    memory: 1024Mi

# JVM settings for production (as per original start.sh)
jvm:
  xms: 1024m
  xmx: 1024m
  xmn: 256m
  metaspaceSize: 512m
  maxMetaspaceSize: 1024m

# Production database configuration
database:
  host: onebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com
  port: 3306
  name: fpaas
  username: fpaas
  password: "production-password-change-me"

# Production MinIO configuration
minio:
  endpoint: http://minio-host:9000
  accessKey: minioadmin
  secretKey: production-minio-secret-change-me
  buckets: fpaas-bucket-prod

# Production Nacos configuration
nacos:
  serverAddr: nacos-host
  namespace: hca0m5ezga95_xkf6m5ezgqn9

# Production Consul configuration
consul:
  host: consul-host
  port: 8500

# Production ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: fpaas-file-upload.yourcompany.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: fpaas-file-upload-prod-tls
      hosts:
        - fpaas-file-upload.yourcompany.com

# Enable autoscaling in production
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Production monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: monitoring
    interval: 15s

# Production persistence
persistence:
  enabled: true
  storageClass: "gp2"
  size: 100Gi

# Production file upload configuration
fileUpload:
  splitSize: 1024
  tmpExpireDays: 7
  taskMaxThread: 8
  taskCoreThread: 2
  taskQueueSize: 100
  encryptType: SM4
  authAvailable: false
  sm4Key: "production-sm4-key-32-chars-long"
  authUrl: "http://production-gateway:8999/webapi/FPAAS-FILE-UPLOAD/heartbeat"

# Production-specific pod annotations
podAnnotations:
  environment: "production"
  
# Production affinity for high availability
affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchExpressions:
        - key: app.kubernetes.io/name
          operator: In
          values:
          - fpaas-file-upload
      topologyKey: kubernetes.io/hostname
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      preference:
        matchExpressions:
        - key: node-type
          operator: In
          values:
          - application

# Production tolerations for dedicated nodes
tolerations:
- key: "dedicated"
  operator: "Equal"
  value: "application"
  effect: "NoSchedule"

# Production node selector
nodeSelector:
  environment: production
