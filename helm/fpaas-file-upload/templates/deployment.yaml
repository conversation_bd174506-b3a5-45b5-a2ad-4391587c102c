apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "fpaas-file-upload.fullname" . }}
  labels:
    {{- include "fpaas-file-upload.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "fpaas-file-upload.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "fpaas-file-upload.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "fpaas-file-upload.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "fpaas-file-upload.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.config.server.port }}
              protocol: TCP
            - name: management
              containerPort: {{ .Values.config.management.server.port }}
              protocol: TCP
          env:
            - name: JAVA_OPTS
              value: {{ include "fpaas-file-upload.jvmOpts" . | quote }}
            - name: SPRING_PROFILES_ACTIVE
              value: {{ .Values.config.spring.profiles.active | quote }}
            - name: SERVER_PORT
              value: {{ .Values.config.server.port | quote }}
            - name: MANAGEMENT_SERVER_PORT
              value: {{ .Values.config.management.server.port | quote }}
            - name: SPRING_APPLICATION_NAME
              value: {{ .Values.config.spring.application.name | quote }}
            - name: ENVIRONMENT
              value: {{ include "fpaas-file-upload.environment" . | quote }}
          envFrom:
            - configMapRef:
                name: {{ include "fpaas-file-upload.fullname" . }}-config
            - secretRef:
                name: {{ include "fpaas-file-upload.fullname" . }}-secret
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: config-volume
              mountPath: /app/config
              readOnly: true
            {{- if .Values.persistence.enabled }}
            - name: temp-storage
              mountPath: {{ .Values.persistence.mountPath }}
            {{- end }}
            - name: logs-volume
              mountPath: /app/logs
      volumes:
        - name: config-volume
          configMap:
            name: {{ include "fpaas-file-upload.fullname" . }}-config
        {{- if .Values.persistence.enabled }}
        - name: temp-storage
          persistentVolumeClaim:
            claimName: {{ include "fpaas-file-upload.fullname" . }}-pvc
        {{- else }}
        - name: temp-storage
          emptyDir: {}
        {{- end }}
        - name: logs-volume
          emptyDir: {}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
