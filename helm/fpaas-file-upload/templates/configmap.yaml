apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "fpaas-file-upload.fullname" . }}-config
  labels:
    {{- include "fpaas-file-upload.labels" . | nindent 4 }}
data:
  # Server configuration
  SERVER_PORT: {{ .Values.config.server.port | quote }}
  MANAGEMENT_SERVER_PORT: {{ .Values.config.management.server.port | quote }}
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: {{ .Values.config.management.endpoints.web.exposure.include | quote }}
  
  # Spring configuration
  SPRING_APPLICATION_NAME: {{ .Values.config.spring.application.name | quote }}
  SPRING_PROFILES_ACTIVE: {{ .Values.config.spring.profiles.active | quote }}
  
  # Database configuration
  SPRING_DATASOURCE_URL: {{ include "fpaas-file-upload.databaseUrl" . | quote }}
  SPRING_DATASOURCE_USERNAME: {{ .Values.database.username | quote }}
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "com.mysql.jdbc.Driver"
  SPRING_DATASOURCE_TYPE: "com.alibaba.druid.pool.DruidDataSource"
  
  # Druid connection pool configuration
  SPRING_DATASOURCE_DRUID_INITIAL_SIZE: "5"
  SPRING_DATASOURCE_DRUID_MIN_IDLE: "5"
  SPRING_DATASOURCE_DRUID_MAX_ACTIVE: "20"
  SPRING_DATASOURCE_DRUID_MAX_WAIT: "60000"
  
  # File upload configuration
  SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE: {{ .Values.fileUpload.maxFileSize | quote }}
  SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE: {{ .Values.fileUpload.maxRequestSize | quote }}
  
  # FPAAS file upload configuration
  FPAAS_FILE_UPLOAD_SPLIT_SIZE: {{ .Values.fileUpload.splitSize | quote }}
  FPAAS_FILE_UPLOAD_UPLOAD_DIR: {{ .Values.fileUpload.uploadDir | quote }}
  FPAAS_FILE_UPLOAD_TMP_DIR: {{ .Values.fileUpload.tmpDir | quote }}
  FPAAS_FILE_UPLOAD_TMP_EXPIRE_DAYS: {{ .Values.fileUpload.tmpExpireDays | quote }}
  FPAAS_FILE_UPLOAD_TASK_MAX_THREAD: {{ .Values.fileUpload.taskMaxThread | quote }}
  FPAAS_FILE_UPLOAD_TASK_CORE_THREAD: {{ .Values.fileUpload.taskCoreThread | quote }}
  FPAAS_FILE_UPLOAD_TASK_QUEUE_SIZE: {{ .Values.fileUpload.taskQueueSize | quote }}
  FPAAS_FILE_UPLOAD_ENCRYPT_TYPE: {{ .Values.fileUpload.encryptType | quote }}
  FPAAS_FILE_UPLOAD_AUTH_AVAILABLE: {{ .Values.fileUpload.authAvailable | quote }}
  
  # MinIO configuration
  EC_OSS_MINIO_ENABLE: "true"
  EC_OSS_MINIO_ENDPOINT: {{ include "fpaas-file-upload.minioEndpoint" . | quote }}
  EC_OSS_MINIO_ACCESS_KEY: {{ .Values.minio.accessKey | quote }}
  EC_OSS_MINIO_BUCKETS: {{ .Values.minio.buckets | quote }}
  
  # File upload OSS configuration
  EC_FILE_UPLOAD_OSS_ENABLE: "true"
  EC_FILE_UPLOAD_OSS_STORE_PATH: "/oss/"
  EC_FILE_UPLOAD_OSS_VALIDATE_CONTENT: "false"
  EC_FILE_UPLOAD_OSS_MAX_REQUEST_SIZE: "-1"
  EC_FILE_UPLOAD_OSS_MAX_FILE_SIZE: "-1"
  
  # File download OSS configuration
  EC_FILE_DOWNLOAD_OSS_ENABLE: "true"
  
  # Nacos configuration
  {{- if .Values.nacos.serverAddr }}
  SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: {{ include "fpaas-file-upload.nacosServerAddr" . | quote }}
  SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: {{ include "fpaas-file-upload.nacosServerAddr" . | quote }}
  SPRING_CLOUD_NACOS_CONFIG_FILE_EXTENSION: "yaml"
  SPRING_CLOUD_NACOS_CONFIG_GROUP: "DEFAULT_GROUP"
  SPRING_CLOUD_NACOS_CONFIG_TIMEOUT: "5000"
  SPRING_CLOUD_NACOS_DISCOVERY_GROUP: "DEFAULT_GROUP"
  {{- if .Values.nacos.namespace }}
  SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE: {{ .Values.nacos.namespace | quote }}
  {{- end }}
  SPRING_CLOUD_NACOS_CONFIG_ENABLED: "true"
  SPRING_CLOUD_NACOS_DISCOVERY_ENABLED: "true"
  {{- else }}
  SPRING_CLOUD_NACOS_CONFIG_ENABLED: "false"
  SPRING_CLOUD_NACOS_DISCOVERY_ENABLED: "false"
  {{- end }}
  
  # Consul configuration
  {{- if .Values.consul.host }}
  SPRING_CLOUD_CONSUL_HOST: {{ .Values.consul.host | quote }}
  SPRING_CLOUD_CONSUL_PORT: {{ .Values.consul.port | quote }}
  SPRING_CLOUD_CONSUL_CONFIG_FORMAT: "YAML"
  SPRING_CLOUD_CONSUL_CONFIG_PREFIX: "config/${SPRING_APPLICATION_NAME}"
  SPRING_CLOUD_CONSUL_CONFIG_DEFAULT_CONTEXT: "application"
  SPRING_CLOUD_CONSUL_CONFIG_DATA_KEY: "data"
  SPRING_CLOUD_CONSUL_DISCOVERY_PREFER_IP_ADDRESS: "true"
  SPRING_CLOUD_CONSUL_DISCOVERY_HEALTH_CHECK_INTERVAL: "15s"
  SPRING_CLOUD_CONSUL_DISCOVERY_HEALTH_CHECK_CRITICAL_TIMEOUT: "30s"
  SPRING_CLOUD_CONSUL_DISCOVERY_HEARTBEAT_REREGISTER_SERVICE_ON_FAILURE: "true"
  SPRING_CLOUD_CONSUL_ENABLED: "true"
  SPRING_CLOUD_CONSUL_CONFIG_ENABLED: "true"
  SPRING_CLOUD_CONSUL_DISCOVERY_ENABLED: "true"
  {{- else }}
  SPRING_CLOUD_CONSUL_ENABLED: "false"
  SPRING_CLOUD_CONSUL_CONFIG_ENABLED: "false"
  SPRING_CLOUD_CONSUL_DISCOVERY_ENABLED: "false"
  {{- end }}
  
  # Galaxy configuration
  GALAXY_APP_ID: {{ .Values.config.spring.application.name | quote }}
  GALAXY_DATA_CENTER: "dc01"
  GALAXY_LOGIC_UNIT_ID: ""
  GALAXY_PHY_UNIT_ID: ""
  GALAXY_PROFILE: {{ include "fpaas-file-upload.environment" . | quote }}
  GALAXY_TENANT_ID: "fpaas"
  
  # Logging configuration
  LOGGING_CONFIG: "classpath:logback-spring.xml"
  
  # Spring Cloud configuration
  SPRING_CLOUD_INETUTILS_USE_ONLY_SITE_LOCAL_INTERFACES: "true"
