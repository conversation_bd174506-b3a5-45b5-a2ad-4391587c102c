apiVersion: v1
kind: Secret
metadata:
  name: {{ include "fpaas-file-upload.fullname" . }}-secret
  labels:
    {{- include "fpaas-file-upload.labels" . | nindent 4 }}
type: Opaque
data:
  # Database password (base64 encoded)
  SPRING_DATASOURCE_PASSWORD: {{ .Values.database.password | default "changeme" | b64enc | quote }}
  
  # MinIO secret key (base64 encoded)
  EC_OSS_MINIO_SECRET_KEY: {{ .Values.minio.secretKey | default "minioadmin" | b64enc | quote }}
  
  # SM4 encryption key for file encryption (base64 encoded)
  # This should be a 32-character SM4 symmetric key
  FPAAS_FILE_UPLOAD_SM4_KEY: {{ .Values.fileUpload.sm4Key | default "12345678901234567890123456789012" | b64enc | quote }}
  
  # Auth service URL (if auth is enabled)
  FPAAS_FILE_UPLOAD_AUTH_URL: {{ .Values.fileUpload.authUrl | default "http://127.0.0.1:8999/webapi/FPAAS-FILE-UPLOAD/heartbeat" | b64enc | quote }}
