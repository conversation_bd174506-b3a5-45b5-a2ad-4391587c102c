apiVersion: v1
kind: Service
metadata:
  name: {{ include "fpaas-file-upload.fullname" . }}
  labels:
    {{- include "fpaas-file-upload.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.config.management.server.port }}
      targetPort: management
      protocol: TCP
      name: management
  selector:
    {{- include "fpaas-file-upload.selectorLabels" . | nindent 4 }}
---
{{- if .Values.monitoring.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "fpaas-file-upload.fullname" . }}-metrics
  labels:
    {{- include "fpaas-file-upload.labels" . | nindent 4 }}
    app.kubernetes.io/component: metrics
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.config.management.server.port }}
      targetPort: management
      protocol: TCP
      name: metrics
  selector:
    {{- include "fpaas-file-upload.selectorLabels" . | nindent 4 }}
{{- end }}
