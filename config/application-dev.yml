spring:
  cloud:
    nacos:
      config:
        server-addr: dev-nacos-host:8848
        file-extension: yaml
        group: DEFAULT_GROUP
        timeout: 5000
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        group: DEFAULT_GROUP
    consul:
      host: dev-consul-host
      port: 8500
      config:
        # 对应配置为/config/IAM-AAS/application/data，格式为YAML
        format: YAML
        prefix: config/${spring.application.name}
        defaultContext: application
        dataKey: data
      discovery:
        prefer-ip-address: true
        healthCheckInterval: 15s
        healthCheckCriticalTimeout: 30s
        heartbeat:
          reregisterServiceOnFailure: true #掉线重连
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: *****************************************************************************************************************************************************************************
    username: deploy
    password: 1q2w3e4R!@#$
    druid:
      initialSize: 5
      minIdle: 5
      maxActive: 20
      maxWait: 60000
  servlet:
    multipart:
      # 单个文件大小为5MB
      max-file-size: 100MB
      # 总上传的数据大小5MB
      max-request-size: 100MB
fpaas:
  file-upload:
    # 单位KB，默认1024
    split-size: 1024
    # oss上传目录
    upload-dir: file-upload
    # 临时文件目录，分片文件会暂存在此目录，不填默认为用户临时目录
    tmp-dir: /tmp/fpaas-temp
    # 临时文件过期时间，天。TODO
    tmp-expire-days: 7
    # 异步任务参数
    task-max-thread: 8
    task-core-thread: 2
    # 任务队列
    task-queue-size: 100
    # 文件加解密类型，NONE/SM2/RSA/SM4
    encrypt-type: SM4
    # 鉴权开关，部署不经过网关时需要开启鉴权
    auth-available: false
    # 鉴权服务，过网关能正常返回即可
    auth-url: http://127.0.0.1:8999/webapi/FPAAS-FILE-UPLOAD/heartbeat
galaxy:
  appId: ${spring.application.name}
  dataCenter: dc01
  logicUnitId: ''
  phyUnitId: ''
  profile: dev
  tenantId: fpaas
logging:
  config: classpath:logback-spring.xml
# oss上传配置
ec:
  file:
    download:
      oss:
        enable: true
    upload:
      oss:
        enable: true
        storePath: '/oss/'
        validateContent: false
        maxRequestSize: -1
        maxFileSize: -1
        supportExtensions:
          - jpg
          - jpeg
          - gif
          - png
          - webp
          - pdf
          - docx
          - mp4
          - ogg
          - webm
          - zip
          - rar
          - css
          - json
          - html
          - js
          - ttf
          - woff
          - woff2
          - txt
          - ipd
          - xls
          - xlsx
          - svg
          - mp3
          - vqf
          - wav
          - aac
          - wave
          - avi
          - amr
          - mov
          - flv
          - p8
          - p12
  oss:
    minio:
      enable: true
      accessKey: minioadmin
      secretKey: minioadmin
      endpoint: http://dev-minio-host:9000
      view:
      buckets: fpaas-bucket
management:
  endpoints:
    web:
      exposure:
        exclude: env,beans
        include: '*'
  metrics:
    tags:
      application: ${spring.application.name}