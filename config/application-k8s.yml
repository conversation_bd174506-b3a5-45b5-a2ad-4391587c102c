# Kubernetes-specific configuration for FPAAS File Upload Service
# This configuration is optimized for containerized deployment

spring:
  application:
    name: ${SPRING_APPLICATION_NAME:FPAAS-FILE-UPLOAD}
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:k8s}
  
  # Database configuration using environment variables
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: ${SPRING_DATASOURCE_DRIVER_CLASS_NAME:com.mysql.jdbc.Driver}
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    druid:
      initialSize: ${SPRING_DATASOURCE_DRUID_INITIAL_SIZE:5}
      minIdle: ${SPRING_DATASOURCE_DRUID_MIN_IDLE:5}
      maxActive: ${SPRING_DATASOURCE_DRUID_MAX_ACTIVE:20}
      maxWait: ${SPRING_DATASOURCE_DRUID_MAX_WAIT:60000}
      
  # File upload configuration
  servlet:
    multipart:
      max-file-size: ${SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE:100MB}
      max-request-size: ${SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE:100MB}
      
  # Cloud configuration
  cloud:
    inetutils:
      use-only-site-local-interfaces: ${SPRING_CLOUD_INETUTILS_USE_ONLY_SITE_LOCAL_INTERFACES:true}
    nacos:
      config:
        enabled: ${SPRING_CLOUD_NACOS_CONFIG_ENABLED:false}
        server-addr: ${SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR:}
        file-extension: ${SPRING_CLOUD_NACOS_CONFIG_FILE_EXTENSION:yaml}
        group: ${SPRING_CLOUD_NACOS_CONFIG_GROUP:DEFAULT_GROUP}
        timeout: ${SPRING_CLOUD_NACOS_CONFIG_TIMEOUT:5000}
      discovery:
        enabled: ${SPRING_CLOUD_NACOS_DISCOVERY_ENABLED:false}
        server-addr: ${SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR:}
        group: ${SPRING_CLOUD_NACOS_DISCOVERY_GROUP:DEFAULT_GROUP}
        namespace: ${SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE:}
    consul:
      enabled: ${SPRING_CLOUD_CONSUL_ENABLED:false}
      host: ${SPRING_CLOUD_CONSUL_HOST:}
      port: ${SPRING_CLOUD_CONSUL_PORT:8500}
      config:
        enabled: ${SPRING_CLOUD_CONSUL_CONFIG_ENABLED:false}
        format: ${SPRING_CLOUD_CONSUL_CONFIG_FORMAT:YAML}
        prefix: ${SPRING_CLOUD_CONSUL_CONFIG_PREFIX:config/${spring.application.name}}
        defaultContext: ${SPRING_CLOUD_CONSUL_CONFIG_DEFAULT_CONTEXT:application}
        dataKey: ${SPRING_CLOUD_CONSUL_CONFIG_DATA_KEY:data}
      discovery:
        enabled: ${SPRING_CLOUD_CONSUL_DISCOVERY_ENABLED:false}
        prefer-ip-address: ${SPRING_CLOUD_CONSUL_DISCOVERY_PREFER_IP_ADDRESS:true}
        healthCheckInterval: ${SPRING_CLOUD_CONSUL_DISCOVERY_HEALTH_CHECK_INTERVAL:15s}
        healthCheckCriticalTimeout: ${SPRING_CLOUD_CONSUL_DISCOVERY_HEALTH_CHECK_CRITICAL_TIMEOUT:30s}
        heartbeat:
          reregisterServiceOnFailure: ${SPRING_CLOUD_CONSUL_DISCOVERY_HEARTBEAT_REREGISTER_SERVICE_ON_FAILURE:true}

# Server configuration
server:
  port: ${SERVER_PORT:19020}

# Management endpoints configuration
management:
  server:
    port: ${MANAGEMENT_SERVER_PORT:19021}
  endpoints:
    web:
      exposure:
        include: ${MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE:health,info,metrics,prometheus}
        exclude: env,beans
  endpoint:
    health:
      probes:
        enabled: true
      show-details: always
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
      environment: ${ENVIRONMENT:k8s}

# FPAAS file upload configuration
fpaas:
  file-upload:
    split-size: ${FPAAS_FILE_UPLOAD_SPLIT_SIZE:1024}
    upload-dir: ${FPAAS_FILE_UPLOAD_UPLOAD_DIR:file-upload}
    tmp-dir: ${FPAAS_FILE_UPLOAD_TMP_DIR:/tmp/fpaas-temp}
    tmp-expire-days: ${FPAAS_FILE_UPLOAD_TMP_EXPIRE_DAYS:7}
    task-max-thread: ${FPAAS_FILE_UPLOAD_TASK_MAX_THREAD:8}
    task-core-thread: ${FPAAS_FILE_UPLOAD_TASK_CORE_THREAD:2}
    task-queue-size: ${FPAAS_FILE_UPLOAD_TASK_QUEUE_SIZE:100}
    encrypt-type: ${FPAAS_FILE_UPLOAD_ENCRYPT_TYPE:SM4}
    auth-available: ${FPAAS_FILE_UPLOAD_AUTH_AVAILABLE:false}
    auth-url: ${FPAAS_FILE_UPLOAD_AUTH_URL:http://127.0.0.1:8999/webapi/FPAAS-FILE-UPLOAD/heartbeat}

# Galaxy configuration
galaxy:
  appId: ${GALAXY_APP_ID:${spring.application.name}}
  dataCenter: ${GALAXY_DATA_CENTER:dc01}
  logicUnitId: ${GALAXY_LOGIC_UNIT_ID:}
  phyUnitId: ${GALAXY_PHY_UNIT_ID:}
  profile: ${GALAXY_PROFILE:k8s}
  tenantId: ${GALAXY_TENANT_ID:fpaas}

# Logging configuration
logging:
  config: ${LOGGING_CONFIG:classpath:logback-spring.xml}

# OSS configuration
ec:
  file:
    download:
      oss:
        enable: ${EC_FILE_DOWNLOAD_OSS_ENABLE:true}
    upload:
      oss:
        enable: ${EC_FILE_UPLOAD_OSS_ENABLE:true}
        storePath: ${EC_FILE_UPLOAD_OSS_STORE_PATH:/oss/}
        validateContent: ${EC_FILE_UPLOAD_OSS_VALIDATE_CONTENT:false}
        maxRequestSize: ${EC_FILE_UPLOAD_OSS_MAX_REQUEST_SIZE:-1}
        maxFileSize: ${EC_FILE_UPLOAD_OSS_MAX_FILE_SIZE:-1}
        supportExtensions:
          - jpg
          - jpeg
          - gif
          - png
          - webp
          - pdf
          - docx
          - mp4
          - ogg
          - webm
          - zip
          - rar
          - css
          - json
          - html
          - js
          - ttf
          - woff
          - woff2
          - txt
          - ipd
          - xls
          - xlsx
          - svg
          - mp3
          - vqf
          - wav
          - aac
          - wave
          - avi
          - amr
          - mov
          - flv
          - p8
          - p12
  oss:
    minio:
      enable: ${EC_OSS_MINIO_ENABLE:true}
      accessKey: ${EC_OSS_MINIO_ACCESS_KEY:minioadmin}
      secretKey: ${EC_OSS_MINIO_SECRET_KEY:minioadmin}
      endpoint: ${EC_OSS_MINIO_ENDPOINT:http://minio-host:9000}
      view: ${EC_OSS_MINIO_VIEW:}
      buckets: ${EC_OSS_MINIO_BUCKETS:fpaas-bucket}
