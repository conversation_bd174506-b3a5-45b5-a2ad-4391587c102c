<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

  <context id="dev">
  
    <commentGenerator>
      <property name="suppressDate" value="true"/>
    </commentGenerator>

    <jdbcConnection connectionURL="**********************************************************************************************************************************************************************************" driverClass="com.mysql.jdbc.Driver" userId="root" password="1q2w3e4R!@#$" />
    <javaModelGenerator targetPackage="fpaas.upload.dao.autogen" targetProject="${model.target.dir}" />
    <sqlMapGenerator targetPackage="dataservice.autogen" targetProject="${xml.target.dir}" />
    <javaClientGenerator targetPackage="fpaas.upload.dao.mapper.autogen" targetProject="MAVEN" type="XMLMAPPER" >
    </javaClientGenerator>

    <table tableName="UPLOAD_FILE" domainObjectName="UploadFile" enableSelectByExample="true" enableDeleteByExample="false" enableCountByExample="false" enableUpdateByExample="false" >
        <property name="rootInterface" value="fpaas.upload.dao.mapper.UploadFileBase" />
    </table>
    <table tableName="APP_WS" domainObjectName="AppWs" enableSelectByExample="true" enableDeleteByExample="false" enableCountByExample="false" enableUpdateByExample="false" >
        <property name="rootInterface" value="fpaas.upload.dao.mapper.AppWsBase" />
    </table>
    <table tableName="APP_KEY" domainObjectName="AppKey" enableSelectByExample="true" enableDeleteByExample="false" enableCountByExample="false" enableUpdateByExample="false" >
        <property name="rootInterface" value="fpaas.upload.dao.mapper.AppKeyBase" />
    </table>

  </context>
</generatorConfiguration>