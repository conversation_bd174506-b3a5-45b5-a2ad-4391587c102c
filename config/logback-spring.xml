<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="fpaas-logback.xml"/>

    <root level="INFO">
        <appender-ref ref="console"/>
        <!-- uncomment this to have also JSON logs -->
        <!--<appender-ref ref="logstash"/>-->
        <!--<appender-ref ref="logstash_async"/>-->
        <!--<appender-ref ref="flatfile"/>-->
        <!--<appender-ref ref="flatfile_async"/>-->
        <!--<appender-ref ref="jupiter_file" />-->
        <appender-ref ref="jupiter_file_async" />
    </root>

    <logger name="org.springframework" level="warn"/>
    <logger name="fpaas.upload.dao" level="error"/>
    <logger name="fpaas.upload" level="debug"/>
</configuration>
