#!/bin/bash

# FPAAS File Upload Service - Build and Deploy Script
# This script builds the Docker image and deploys to Kubernetes using Helm

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CHART_PATH="$PROJECT_ROOT/helm/fpaas-file-upload"

# Default values
ENVIRONMENT="dev"
REGISTRY="************.dkr.ecr.ap-southeast-1.amazonaws.com"
NAMESPACE="onebank"
IMAGE_TAG="0.0.1-alpha"
DRY_RUN=false
SKIP_BUILD=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Build and deploy FPAAS File Upload Service to Kubernetes

OPTIONS:
    -e, --environment ENV    Target environment (dev|staging|prod) [default: dev]
    -r, --registry REGISTRY  Container registry URL
    -n, --namespace NS       Kubernetes namespace [default: fpaas-ENV]
    -t, --tag TAG           Image tag [default: ENV-latest or version from Chart.yaml]
    -d, --dry-run           Perform a dry run without actual deployment
    -s, --skip-build        Skip Docker image build
    -h, --help              Show this help message

EXAMPLES:
    # Deploy to development
    $0 -e dev -r your-registry.com

    # Deploy to production with specific tag
    $0 -e prod -r your-registry.com -t 2.6.0

    # Dry run for staging
    $0 -e staging -r your-registry.com -d

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -s|--skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod."
    exit 1
fi

# Set defaults based on environment
if [[ -z "$NAMESPACE" ]]; then
    NAMESPACE="fpaas-$ENVIRONMENT"
fi

if [[ -z "$IMAGE_TAG" ]]; then
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        IMAGE_TAG=$(grep '^appVersion:' "$CHART_PATH/Chart.yaml" | sed 's/appVersion: *"\(.*\)"/\1/')
    else
        IMAGE_TAG="$ENVIRONMENT-latest"
    fi
fi

# Validate required parameters
if [[ -z "$REGISTRY" ]]; then
    print_error "Registry URL is required. Use -r or --registry option."
    exit 1
fi

# Set image name
IMAGE_NAME="$REGISTRY/onebank/fpaas-file-upload:$IMAGE_TAG"

print_info "Starting deployment with the following configuration:"
print_info "  Environment: $ENVIRONMENT"
print_info "  Registry: $REGISTRY"
print_info "  Namespace: $NAMESPACE"
print_info "  Image: $IMAGE_NAME"
print_info "  Dry Run: $DRY_RUN"
print_info "  Skip Build: $SKIP_BUILD"

# Build Docker image
if [[ "$SKIP_BUILD" == "false" ]]; then
    print_info "Building Docker image..."
    cd "$PROJECT_ROOT"
    
    if ! docker build -t "$IMAGE_NAME" .; then
        print_error "Docker build failed"
        exit 1
    fi
    
    print_success "Docker image built successfully"
    
    # Push image to registry
    print_info "Pushing image to registry..."
    if ! docker push "$IMAGE_NAME"; then
        print_error "Docker push failed"
        exit 1
    fi
    
    print_success "Image pushed to registry successfully"
else
    print_warning "Skipping Docker build as requested"
fi

# Prepare Helm command
HELM_CMD="helm"
if [[ "$DRY_RUN" == "true" ]]; then
    HELM_CMD="$HELM_CMD --dry-run"
    print_warning "Performing dry run - no actual deployment will occur"
fi

VALUES_FILE="$CHART_PATH/values-$ENVIRONMENT.yaml"
if [[ ! -f "$VALUES_FILE" ]]; then
    print_error "Values file not found: $VALUES_FILE"
    exit 1
fi

# Deploy with Helm
print_info "Deploying to Kubernetes..."

$HELM_CMD upgrade --install fpaas-file-upload "$CHART_PATH" \
    -f "$VALUES_FILE" \
    --namespace "$NAMESPACE" \
    --create-namespace \
    --set image.registry="$REGISTRY" \
    --set image.tag="$IMAGE_TAG" \
    --timeout 10m \
    --wait

if [[ "$DRY_RUN" == "false" ]]; then
    print_success "Deployment completed successfully!"
    
    print_info "Checking deployment status..."
    kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=fpaas-file-upload
    
    print_info "Service endpoints:"
    kubectl get svc -n "$NAMESPACE" -l app.kubernetes.io/name=fpaas-file-upload
    
    if kubectl get ingress -n "$NAMESPACE" fpaas-file-upload >/dev/null 2>&1; then
        print_info "Ingress configuration:"
        kubectl get ingress -n "$NAMESPACE" fpaas-file-upload
    fi
else
    print_success "Dry run completed successfully!"
fi
