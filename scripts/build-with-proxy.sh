#!/bin/bash

# Build script with proxy configuration for FPAAS File Upload Service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values from your configuration
REGISTRY="************.dkr.ecr.ap-southeast-1.amazonaws.com"
IMAGE_TAG="0.0.1-alpha"
IMAGE_NAME="$REGISTRY/onebank/fpaas-file-upload:$IMAGE_TAG"

print_info "Starting Docker build with proxy configuration..."
print_info "Image: $IMAGE_NAME"

# Set up proxy environment variables
print_info "Configuring proxy settings..."
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890

# Also set uppercase versions for compatibility
export HTTPS_PROXY=$https_proxy
export HTTP_PROXY=$http_proxy
export ALL_PROXY=$all_proxy

print_success "Proxy configuration set:"
print_info "  HTTP_PROXY: $HTTP_PROXY"
print_info "  HTTPS_PROXY: $HTTPS_PROXY"
print_info "  ALL_PROXY: $ALL_PROXY"

# Change to project root
cd "$PROJECT_ROOT"

# Build Docker image with proxy settings
print_info "Building Docker image..."
if docker build \
    --build-arg HTTP_PROXY="$HTTP_PROXY" \
    --build-arg HTTPS_PROXY="$HTTPS_PROXY" \
    --build-arg http_proxy="$http_proxy" \
    --build-arg https_proxy="$https_proxy" \
    --build-arg ALL_PROXY="$ALL_PROXY" \
    --build-arg all_proxy="$all_proxy" \
    -t "$IMAGE_NAME" .; then
    
    print_success "Docker image built successfully!"
    
    # Show image details
    print_info "Image details:"
    docker images "$IMAGE_NAME"
    
    # Test the image
    print_info "Testing the built image..."
    if docker run --rm "$IMAGE_NAME" java -version; then
        print_success "Image test passed!"
    else
        print_warning "Image test failed, but build was successful"
    fi
    
else
    print_error "Docker build failed"
    exit 1
fi

print_info "Build completed successfully!"
print_info "You can now push the image with:"
print_info "  docker push $IMAGE_NAME"
print_info ""
print_info "Or deploy using the build-and-deploy script:"
print_info "  ./scripts/build-and-deploy.sh -e dev -s"
