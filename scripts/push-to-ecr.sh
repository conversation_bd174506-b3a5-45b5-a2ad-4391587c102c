#!/bin/bash

# Push Docker image to ECR script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
REGISTRY="************.dkr.ecr.ap-southeast-1.amazonaws.com"
REPOSITORY="onebank/fpaas-file-upload"
TAG="0.0.1-alpha"
REGION="ap-southeast-1"
IMAGE_NAME="$REGISTRY/$REPOSITORY:$TAG"

print_info "Starting ECR push process..."
print_info "Image: $IMAGE_NAME"
print_info "Region: $REGION"

# Set up proxy environment variables
print_info "Configuring proxy settings..."
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890
export HTTPS_PROXY=$https_proxy
export HTTP_PROXY=$http_proxy
export ALL_PROXY=$all_proxy

print_success "Proxy configuration set"

# Check if image exists locally
print_info "Checking if image exists locally..."
if ! docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "$IMAGE_NAME"; then
    print_error "Image $IMAGE_NAME not found locally!"
    print_info "Available images:"
    docker images --format "table {{.Repository}}:{{.Tag}}" | grep fpaas-file-upload || echo "No fpaas-file-upload images found"
    exit 1
fi

print_success "Image found locally"

# Authenticate with ECR
print_info "Authenticating with ECR..."
if aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $REGISTRY; then
    print_success "ECR authentication successful"
else
    print_error "ECR authentication failed"
    exit 1
fi

# Create repository if it doesn't exist
print_info "Creating ECR repository if it doesn't exist..."
if aws ecr create-repository --repository-name $REPOSITORY --region $REGION >/dev/null 2>&1; then
    print_success "ECR repository created: $REPOSITORY"
else
    print_warning "Repository might already exist (this is normal)"
fi

# Push the image
print_info "Pushing image to ECR..."
if docker push $IMAGE_NAME; then
    print_success "Image pushed successfully to ECR!"
else
    print_error "Failed to push image to ECR"
    exit 1
fi

# Verify the image was pushed
print_info "Verifying image in ECR..."
if aws ecr describe-images --repository-name $REPOSITORY --region $REGION --image-ids imageTag=$TAG >/dev/null 2>&1; then
    print_success "Image verified in ECR repository"
    
    # Show image details
    print_info "Image details:"
    aws ecr describe-images --repository-name $REPOSITORY --region $REGION --image-ids imageTag=$TAG --query 'imageDetails[0].[imageSizeInBytes,imagePushedAt]' --output table
else
    print_error "Failed to verify image in ECR"
    exit 1
fi

print_success "ECR push completed successfully!"
print_info "You can now deploy using:"
print_info "  helm uninstall fpaas-file-upload -n onebank"
print_info "  ./scripts/build-and-deploy.sh -e dev -s"
