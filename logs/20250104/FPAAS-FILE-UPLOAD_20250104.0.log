#[2025-01-04 11:29:58.845] [**********:19020:tenantId_IS_UNDEFINED:profile_IS_UNDEFINED:dataCenter_IS_UNDEFINED:FPAAS-FILE-UPLOAD:logicUnitId_IS_UNDEFINED:phyUnitId_IS_UNDEFINED:TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
#[2025-01-04 11:29:59.741] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  fpaas.upload.UploadApplication.logStartupProfileInfo:638 - The following 1 profile is active: "fat"
#[2025-01-04 11:30:00.849] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.StandardService.log:173 - Starting service [Tomcat]
#[2025-01-04 11:30:00.849] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.StandardEngine.log:173 - Starting Servlet engine: [Apache Tomcat/9.0.83]
#[2025-01-04 11:30:00.918] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:173 - Initializing Spring embedded WebApplicationContext
#[2025-01-04 11:30:01.001] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55 - Init DruidDataSource
#[2025-01-04 11:30:01.319] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
#[2025-01-04 11:30:03.297] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger.logWarning:82 - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
#[2025-01-04 11:30:03.383] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
#[2025-01-04 11:30:03.383] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
#[2025-01-04 11:30:04.097] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, DEFAULT_GROUP FPAAS-FILE-UPLOAD **********:19020 register finished
#[2025-01-04 11:30:04.117] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  fpaas.upload.UploadApplication.logStarted:61 - Started UploadApplication in 5.783 seconds (JVM running for 6.356)
#[2025-01-04 11:31:05.942] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [http-nio-19020-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
#[2025-01-04 14:58:12.708] [**********:19020:tenantId_IS_UNDEFINED:profile_IS_UNDEFINED:dataCenter_IS_UNDEFINED:FPAAS-FILE-UPLOAD:logicUnitId_IS_UNDEFINED:phyUnitId_IS_UNDEFINED:TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
#[2025-01-04 14:58:13.325] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  fpaas.upload.UploadApplication.logStartupProfileInfo:638 - The following 1 profile is active: "fat"
#[2025-01-04 14:58:14.512] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.StandardService.log:173 - Starting service [Tomcat]
#[2025-01-04 14:58:14.513] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.StandardEngine.log:173 - Starting Servlet engine: [Apache Tomcat/9.0.83]
#[2025-01-04 14:58:14.584] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:173 - Initializing Spring embedded WebApplicationContext
#[2025-01-04 14:58:14.669] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55 - Init DruidDataSource
#[2025-01-04 14:58:14.990] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
#[2025-01-04 14:58:16.026] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger.logWarning:82 - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
#[2025-01-04 14:58:16.120] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
#[2025-01-04 14:58:16.121] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
#[2025-01-04 14:58:16.844] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, DEFAULT_GROUP FPAAS-FILE-UPLOAD **********:19020 register finished
#[2025-01-04 14:58:16.863] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  fpaas.upload.UploadApplication.logStarted:61 - Started UploadApplication in 4.772 seconds (JVM running for 9.01)
#[2025-01-04 14:58:44.325] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [http-nio-19020-exec-2] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
