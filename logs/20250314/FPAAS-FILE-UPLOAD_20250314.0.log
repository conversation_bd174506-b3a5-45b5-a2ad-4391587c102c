#[2025-03-14 10:32:36.934] [**********:19020:tenantId_IS_UNDEFINED:profile_IS_UNDEFINED:dataCenter_IS_UNDEFINED:FPAAS-FILE-UPLOAD:logicUnitId_IS_UNDEFINED:phyUnitId_IS_UNDEFINED:TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
#[2025-03-14 10:32:37.632] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  fpaas.upload.UploadApplication.logStartupProfileInfo:638 - The following 1 profile is active: "fat"
#[2025-03-14 10:32:39.616] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.StandardService.log:173 - Starting service [Tomcat]
#[2025-03-14 10:32:39.616] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.StandardEngine.log:173 - Starting Servlet engine: [Apache Tomcat/9.0.83]
#[2025-03-14 10:32:39.724] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:173 - Initializing Spring embedded WebApplicationContext
#[2025-03-14 10:32:39.864] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55 - Init DruidDataSource
#[2025-03-14 10:32:40.345] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
#[2025-03-14 10:32:41.845] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger.logWarning:82 - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
#[2025-03-14 10:32:42.005] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
#[2025-03-14 10:32:42.006] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
#[2025-03-14 10:32:43.077] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, DEFAULT_GROUP FPAAS-FILE-UPLOAD **********:19020 register finished
#[2025-03-14 10:32:43.103] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  fpaas.upload.UploadApplication.logStarted:61 - Started UploadApplication in 7.209 seconds (JVM running for 8.199)
