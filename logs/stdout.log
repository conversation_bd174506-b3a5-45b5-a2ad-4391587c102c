2025-03-14 10:32:36.934  INFO 14330 --- [           main] c.a.n.client.env.SearchableProperties    : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.18)

2025-03-14 10:32:37.632  INFO 14330 --- [           main] fpaas.upload.UploadApplication           : The following 1 profile is active: "fat"
2025-03-14 10:32:39.616  INFO 14330 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-03-14 10:32:39.616  INFO 14330 --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-03-14 10:32:39.724  INFO 14330 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-03-14 10:32:39.864  INFO 14330 --- [           main] c.a.d.s.b.a.DruidDataSourceAutoConfigure : Init DruidDataSource
2025-03-14 10:32:40.345  INFO 14330 --- [           main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-03-14 10:32:41.845  WARN 14330 --- [           main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-03-14 10:32:42.005  INFO 14330 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-03-14 10:32:42.006  INFO 14330 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-03-14 10:32:43.077  INFO 14330 --- [           main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP FPAAS-FILE-UPLOAD 10.0.43.82:19020 register finished
2025-03-14 10:32:43.103  INFO 14330 --- [           main] fpaas.upload.UploadApplication           : Started UploadApplication in 7.209 seconds (JVM running for 8.199)
