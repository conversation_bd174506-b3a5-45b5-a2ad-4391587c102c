#[2025-01-06 20:51:54.866] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [Thread-12] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown:136 - [NotifyCenter] Start destroying Publisher
#[2025-01-06 20:51:54.867] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [Thread-12] WARN  com.alibaba.nacos.common.notify.NotifyCenter.shutdown:153 - [NotifyCenter] Destruction of the end
#[2025-01-06 20:51:54.866] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [Thread-15] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:102 - [HttpClientBeanHolder] Start destroying common HttpClient
#[2025-01-06 20:51:54.871] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [Thread-15] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:111 - [HttpClientBeanHolder] Destruction of the end
#[2025-01-06 20:51:54.899] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:95 - De-registering from Nacos Server now...
#[2025-01-06 20:51:54.903] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [SpringApplicationShutdownHook] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:115 - De-registration finished.
#[2025-01-06 20:51:54.923] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
#[2025-01-06 20:51:54.925] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
#[2025-01-06 20:51:58.067] [**********:19020:tenantId_IS_UNDEFINED:profile_IS_UNDEFINED:dataCenter_IS_UNDEFINED:FPAAS-FILE-UPLOAD:logicUnitId_IS_UNDEFINED:phyUnitId_IS_UNDEFINED:TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
#[2025-01-06 20:51:58.443] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  fpaas.upload.UploadApplication.logStartupProfileInfo:638 - The following 1 profile is active: "fat"
#[2025-01-06 20:51:59.643] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.StandardService.log:173 - Starting service [Tomcat]
#[2025-01-06 20:51:59.644] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.StandardEngine.log:173 - Starting Servlet engine: [Apache Tomcat/9.0.83]
#[2025-01-06 20:51:59.717] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:173 - Initializing Spring embedded WebApplicationContext
#[2025-01-06 20:51:59.811] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55 - Init DruidDataSource
#[2025-01-06 20:52:00.137] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
#[2025-01-06 20:52:01.125] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger.logWarning:82 - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
#[2025-01-06 20:52:01.220] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
#[2025-01-06 20:52:01.221] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
#[2025-01-06 20:52:01.954] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, DEFAULT_GROUP FPAAS-FILE-UPLOAD **********:19020 register finished
#[2025-01-06 20:52:01.974] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [main] INFO  fpaas.upload.UploadApplication.logStarted:61 - Started UploadApplication in 4.461 seconds (JVM running for 5.053)
#[2025-01-06 20:52:19.184] [**********:19020:fpaas:dev:dc01:FPAAS-FILE-UPLOAD:::TID: N/A:-:-:-] [http-nio-19020-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].log:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
